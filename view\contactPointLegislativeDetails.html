<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <title>详情</title>
  <link rel="stylesheet" href="../css/vant.css" />
  <link rel="stylesheet" href="../css/common.css" />
  <style type="text/css">
    [v-cloak] {
      display: none;
    }

    .body_box {
      background-color: #FFFFFF;
      padding-bottom: 60px;
    }

    /* 详情信息 */
    .legislative_details {
      padding: 20px;
    }

    .legislative_details_title {
      font-size: 20px;
      font-weight: 800;
      color: #333333;
      line-height: 30px;
      margin-bottom: 10px;
    }

    .legislative_details_tag {
      background: rgba(246, 147, 28, 0.08);
      font-weight: 400;
      font-size: 14px;
      color: #F6931C;
      padding: 3px 10px;
    }

    .legislative_details_status {
      font-weight: 400;
      font-size: 14px;
      color: #F6931C;
    }

    .legislative_details_item {
      color: rgb(51, 51, 51);
      font-weight: 600;
      font-size: 15px;
      padding: 6px 0;
    }

    .legislative_details_item span {
      color: rgb(51, 51, 51);
      font-weight: normal;
    }

    .legislative_details_content {
      font-size: 16px;
      color: #333333;
      line-height: 30px;
      padding: 5px 0;
    }

    /* 征集结果反馈样式 */
    .feedback_section {
      padding: 15px 20px;
      border-top: 10px solid #f8f8f8;
    }

    .feedback_text {
      font-weight: 400;
      font-size: 16px;
      color: #333333;
      line-height: 25px;
      margin-bottom: 10px;
    }

    .file_box {
      display: flex;
      align-items: center;
      border: 1px solid #F4F5F7;
      margin: 5px 0;
      padding: 5px 8px;
    }

    .file_icon_pdf {
      width: 25px;
      height: 28px;
    }

    .file_word_icon {
      width: 25px;
      height: 23px;
    }

    .file_name {
      font-weight: 400;
      font-size: 15px;
      color: #666666;
      margin-left: 10px;
    }

    .feedback_info {
      font-size: 14px;
      color: #999999;
      margin-top: 10px;
    }

    /* 外部链接 */
    .external_links {
      padding: 15px 20px;
      border-top: 10px solid #f8f8f8;
      font-size: 16px;
      color: #148fe0;
    }

    /* 全部评论 */
    .comment_section {
      padding: 15px 20px;
      border-top: 10px solid #f8f8f8;
    }

    .comment_list {
      padding: 8px 0;
    }

    .comment_item {
      margin-bottom: 10px;
      padding-bottom: 10px;
      border-bottom: 1px solid #f5f5f5;
    }

    .comment_item:last-child {
      border-bottom: none;
      margin-bottom: 0;
      padding-bottom: 0;
    }

    .comment_user {
      margin-bottom: 10px;
    }

    .comment_avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      margin-right: 10px;
    }

    .comment_nickname {
      font-size: 16px;
      color: #333;
      font-weight: 600;
    }

    .comment_content {
      font-weight: 400;
      font-size: 15px;
      color: #333;
      line-height: 20px;
      margin-bottom: 10px;
    }

    .comment_time {
      font-size: 14px;
      color: #999;
    }

    .nodata {
      margin: 30px 0;
      text-align: center;
      color: #ccc;
      width: 100%;
      font-size: 14px;
    }

    /* 评论输入框样式 */
    .comment_input_section {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      background: #fff;
      padding: 10px 15px;
      border-top: 1px solid #e5e5e5;
      z-index: 100;
    }

    .comment_input_box {
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .comment_input {
      flex: 1;
      height: 36px;
      border: 1px solid #e5e5e5;
      border-radius: 18px;
      padding: 0 15px;
      font-size: 14px;
      color: #333;
      background: #f8f8f8;
    }

    .comment_input::placeholder {
      color: #999;
    }

    .comment_num {
      font-size: 14px;
      color: #999;
    }

    .comment_count {
      font-size: 12px;
      color: #999;
      display: flex;
      align-items: center;
      gap: 5px;
    }

    .comment_icon {
      width: 23px;
      height: 23px;
      margin-left: 5px;
    }

    /* 评论弹窗样式 */
    .comment_popup {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      z-index: 1000;
      display: flex;
      align-items: flex-end;
    }

    .comment_popup_content {
      width: 100%;
      background: #fff;
      border-radius: 12px 12px 0 0;
      padding: 20px;
      animation: slideUp 0.3s ease-out;
    }

    @keyframes slideUp {
      from {
        transform: translateY(100%);
      }

      to {
        transform: translateY(0);
      }
    }

    .comment_popup_header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }

    .comment_popup_title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }

    .comment_popup_close {
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #999;
      font-size: 18px;
      cursor: pointer;
    }

    .comment_textarea {
      width: 100%;
      min-height: 120px;
      border: 1px solid #e5e5e5;
      border-radius: 8px;
      padding: 12px;
      font-size: 14px;
      color: #333;
      resize: none;
      outline: none;
      box-sizing: border-box;
    }

    .comment_textarea::placeholder {
      color: #999;
    }

    .comment_popup_footer {
      display: flex;
      gap: 15px;
      margin-top: 20px;
    }

    .comment_btn {
      flex: 1;
      height: 44px;
      border: none;
      border-radius: 22px;
      font-size: 16px;
      cursor: pointer;
      transition: all 0.3s;
    }

    .comment_btn_cancel {
      background: #f5f5f5;
      color: #666;
    }

    .comment_btn_submit {
      background: #1890ff;
      color: #fff;
    }

    /* 投票样式 */
    .vote_box {
      padding: 15px 20px;
      border-top: 10px solid #f8f8f8;
    }

    .vote_title {
      text-align: center;
      margin-bottom: 20px;
      font-size: 18px;
      font-weight: 600;
      color: #333;
      line-height: 25px;
    }

    .vote_desc {
      margin-bottom: 20px;
      font-size: 14px;
      color: #666;
    }

    .select_item {
      padding: 10px 0;
      margin-bottom: 10px;
      border-bottom: 1px solid #f0f0f0;
    }

    .option_text {
      font-size: 15px;
      color: #333;
      line-height: 22px;
    }

    .option_count {
      font-size: 14px;
      color: #999;
      margin-left: 10px;
    }

    .option_checkbox {
      width: 18px;
      height: 18px;
      border: 1px solid #ddd;
      border-radius: 50%;
      transition: all 0.3s;
      margin-left: 10px;
    }

    .option_checkbox.checked {
      border-color: #104B8B;
      background-color: #104B8B;
    }

    .checkbox_inner {
      width: 10px;
      height: 10px;
    }

    .checkmark {
      color: white;
      font-size: 12px;
      font-weight: bold;
    }

    .vote_button {
      width: 100%;
      height: 44px;
      background-color: #104B8B;
      border-radius: 22px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s;
    }

    .vote_button.disabled {
      background: #d9d9d9;
      color: #ffffff;
    }

    .vote_button_text {
      font-size: 16px;
      color: white;
      font-weight: 500;
    }

    /* 未拆条文 */
    .un_provision {
      padding: 12px 16px;
      border-top: 10px solid #f8f8f8;
    }

    /* 拆条文 */
    .provision {
      padding: 16px;
      border-top: 10px solid #f8f8f8;
    }

    .m20 {
      margin-bottom: 10px;
    }

    .m24 {
      margin-bottom: 12px;
    }

    .m40 {
      margin-bottom: 20px;
    }

    .common {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      margin-bottom: 10px;
    }

    .common_num {
      color: rgb(102, 102, 102);
      font-size: 15px;
      margin-right: 5px;
    }

    .common_icon {
      width: 20px;
      height: 20px;
    }

    /* 悬浮目录按钮 */
    .catalogue-button {
      position: fixed;
      right: 20px;
      bottom: 100px;
      width: 50px;
      height: 50px;
      background-color: #104B8B;
      color: white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
      z-index: 998;
      cursor: pointer;
    }

    /* 目录面板样式 */
    .catalogue-panel {
      position: fixed;
      top: 0;
      left: -300px;
      /* bottom: 57px; */
      width: 230px;
      background: #fff;
      box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
      z-index: 1001;
      transition: left 0.3s ease;
    }

    .catalogue-panel.show {
      left: 0;
    }

    .catalogue-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px 20px;
      border-bottom: 1px solid #e5e5e5;
      background: #f8f9fa;
    }

    .catalogue-header-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }

    .catalogue-close {
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20px;
      color: #666;
      cursor: pointer;
      border-radius: 50%;
      transition: background-color 0.2s;
    }

    .catalogue-close:hover {
      background-color: #e5e5e5;
    }

    .catalogue-content {
      height: calc(100vh - 60px);
      overflow-y: auto;
    }

    .catalogue-list {
      padding: 10px 0;
    }

    .catalogue-item {
      padding: 8px 20px;
      margin-bottom: 10px;
      border-radius: 4px;
    }

    .catalogue-clause {
      background-color: #fafafa;
    }

    .catalogue-item.catalogue-title {
      font-weight: 600;
      background-color: #f8f9fa;
    }

    .catalogue-text {
      font-size: 14px;
      color: #333;
      line-height: 1.5;
    }

    /* 背景遮罩样式 */
    .catalogue-mask {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.3);
      z-index: 998;
      opacity: 0;
      pointer-events: none;
      transition: opacity 0.3s ease, visibility 0.3s ease;
      /* 阻止触摸事件穿透 */
      touch-action: none;
    }

    .catalogue-mask.show {
      opacity: 1;
      pointer-events: auto;
    }

    /* 当目录显示时，阻止body滚动 */
    body.catalogue-open {
      overflow: hidden;
      position: fixed;
      width: 100%;
    }
  </style>
</head>

<body>
  <div class="body_box" id="app">
    <!-- 详情信息 -->
    <div class="legislative_details" v-cloak>
      <div class="legislative_details_title">{{info.title}}</div>
      <div class="flex_box flex_align_center flex_justify_between" style="margin-bottom: 10px;">
        <div class="legislative_details_tag" v-if="info.legisOpinionType.value">{{info.legisOpinionType.name}}
        </div>
        <div class="legislative_details_status">{{info.enlistStatus}}</div>
      </div>
      <div class="legislative_details_item">发布机构：<span>{{info.officeName}}</span></div>
      <div class="legislative_details_item">开始时间：<span>{{formatTime(info.startTime)}}</span></div>
      <div class="legislative_details_item">结束时间：<span>{{formatTime(info.endTime)}}</span></div>
      <div class="legislative_details_content" v-html="info.opinionContent"></div>
      <div class="file_box" v-for="(item,index) in info.attachments" :key="index" @click="clickfile(item)" v-cloak>
        <image :src="item.extName=='pdf'?'../img/pdf.png':'../img/word3.png'" mode=""
          :class="item.extName=='pdf'?'file_icon_pdf':'file_word_icon'" />
        <div class="file_name one_text">{{item.originalFileName}}</div>
      </div>
    </div>

    <!-- 征集结果反馈 -->
    <div class="feedback_section" v-if="info.feedback">
      <div class="section-header flex_box flex_align_center" style="margin-bottom: 10px;">
        <div class="section-line"></div>
        <span class="section-title">征集结果反馈</span>
      </div>
      <div class="feedback_content" v-cloak>
        <div class="feedback_text" v-html="info.feedback.content"></div>
        <div class="file_box" style="margin: 10px 0;" v-for="(item,index) in info.feedback.attachments" :key="index"
          @click="clickfile(item)" v-cloak>
          <image :src="item.extName=='pdf'?'../img/pdf.png':'../img/word3.png'" mode=""
            :class="item.extName=='pdf'?'file_icon_pdf':'file_word_icon'" />
          <div class="file_name one_text">{{item.originalFileName}}</div>
        </div>
        <div class="feedback_info flex_box flex_align_center flex_justify_between">
          <div class="feedback_author">反馈人：{{info.feedback.createName}}</div>
          <div class="feedback_time">反馈时间：{{formatTime(info.feedback.createDate)}}</div>
        </div>
      </div>
    </div>

    <!-- 投票形式 -->
    <div class="vote_box" v-if="info.opinionLayout.value == '2'" v-cloak>
      <div class="vote_title">{{info.voteTopic.topic}}</div>
      <div class="vote_desc">{{info.voteTopic.maxVote>1?'多选':'单选'}}，最多{{info.voteTopic.maxVote}}项</div>
      <div class="select_item flex_box flex_justify_between flex_align_center"
        v-for="(item,index) in info.voteTopicOptions" :key="index" @click="selectOption(index)">
        <div class="flex_box flex_justify_between flex_align_center flex_1">
          <span class="option_text">{{item.optionContent}}</span>
          <span class="option_count" v-if="info.hasVote">{{item.count}}票</span>
        </div>
        <div class="option_checkbox flex_box flex_justify_center flex_align_center" :class="{'checked': item.choose}">
          <div class="checkbox_inner flex_box flex_justify_center flex_align_center" v-if="item.choose">
            <span class="checkmark">✓</span>
          </div>
        </div>
      </div>
      <div class="vote_button"
        :class="{'disabled': info.hasVote || info.enlistStatus == '已结束' || info.enlistStatus == '未开始'}"
        @click="submitVote">
        <span class="vote_button_text">{{info.hasVote ? '已投票' : (info.enlistStatus == '已结束' || info.enlistStatus ==
          '未开始' ? '投票'+info.enlistStatus : '投票')}}</span>
      </div>
    </div>

    <!-- 外部链接形式 -->
    <div v-if="info.opinionLayout.value == 3" class="external_links" @click="openOutLink" v-cloak>外部链接：{{info.outLink}}
    </div>

    <!-- 拆条文 -->
    <div class="provision" v-if="info.isSplitClause">
      <div v-for="(item,index) in splitClauseList" :key="index">
        <template v-if="item.sectionType == 'SECTION'">
          <div class="m20">
            <div style="color:#333;width:100%;" v-html="item.sectionGraph+item.sectionContent"></div>
          </div>
        </template>
        <template v-else-if="item.sectionType == 'CLAUSE'">
          <div :id="'noti'+item.sort" @click="openDetailsWin(item)">
            <div class="m20">
              <div style="color:#333;width:100%;line-height: 30px;font-size: 16px;"
                v-html="item.sectionGraph + item.sectionContent"></div>
            </div>
            <div class="common">
              <span class="common_num">{{item.opinionNum}}</span>
              <image class="common_icon" :src="item.opinionNum>0?'../img/icon_common_o.png':'../img/icon_common.png'"
                mode="" />
            </div>
          </div>
        </template>
        <template v-else-if="item.sectionType == 'TITLE'">
          <div class="m40">
            <div style="color:#333;text-align: center;width:100%;" v-html="item.sectionContent"></div>
          </div>
        </template>
        <template v-else>
          <div class="m24" v-html="item.sectionContent">
            <div style="color:#333;width:100%;" v-html="item.sectionContent"></div>
          </div>
        </template>
      </div>
    </div>

    <!-- 没有拆条文，需要解析文档中的文本 -->
    <div class="un_provision" v-else-if="info.contentFileIds != ''">
      <div class="file_box" style="margin: 20rpx 0;" v-for="(item,index) in info.contentFileInfos" :key="index"
        @click="clickfile(item)" v-cloak>
        <image :src="item.extName=='pdf'?'../img/pdf.png':'../img/word3.png'" mode=""
          :class="item.extName=='pdf'?'file_icon_pdf':'file_word_icon'" />
        <div class="file_name" v-cloak>{{item.originalFileName}}</div>
      </div>
    </div>

    <!-- 全部评论 -->
    <div class="comment_section" v-if="info.opinionLayout.value != '2'">
      <div class="section-header flex_box flex_align_center" style="margin-bottom: 10px;">
        <div class="section-line"></div>
        <span class="section-title">全部评论</span>
      </div>
      <div class="comment_list">
        <template v-if="commentList&&commentList.length>0">
          <div class="comment_item" v-for="(item,index) in commentList" :key="index">
            <div class="comment_user flex_box flex_align_center">
              <image class="comment_avatar" :src="item.headImg?fileImgUrl+item.headImg:'../img/def_head_img.jpg'"
                mode="aspectFill" />
              <div class="comment_nickname">{{item.createName}}</div>
            </div>
            <div class="comment_content">{{item.commentContent}}</div>
            <div class="comment_time">{{formatTime(item.createDate)}}</div>
          </div>
        </template>
        <template v-else>
          <div class="nodata">暂无数据</div>
        </template>
      </div>
    </div>

    <!-- 评论输入框 -->
    <div class="comment_input_section" v-if="info.opinionLayout.value != '2'">
      <div class="comment_input_box">
        <input type="text" class="comment_input" placeholder="发表评论" readonly @click="showCommentPopup" />
        <div class="comment_count">
          <span class="comment_num" v-cloak>{{commentList.length}}</span>
          <image class="comment_icon" src="../img/icon_common.png" mode="aspectFill" />
        </div>
      </div>
    </div>

    <!-- 评论弹窗 -->
    <div class="comment_popup" v-if="showPopup" @click="hideCommentPopup">
      <div class="comment_popup_content" @click.stop v-cloak>
        <div class="comment_popup_header">
          <div class="comment_popup_title">发表评论</div>
          <div class="comment_popup_close" @click="hideCommentPopup">×</div>
        </div>
        <textarea class="comment_textarea" v-model="commentText" placeholder="请输入您的评论" maxlength="500"></textarea>
        <div class="comment_popup_footer">
          <button class="comment_btn comment_btn_cancel" @click="hideCommentPopup">取消</button>
          <button class="comment_btn comment_btn_submit" @click="submitComment">发送</button>
        </div>
      </div>
    </div>

    <!-- 悬浮目录 -->
    <div class="catalogue-button" @click="openCatalogue" v-if="info.isSplitClause">目录</div>

    <!-- 左侧目录列表 -->
    <div class="catalogue-panel" :class="{'show': showCatalogue}" v-if="info.isSplitClause">
      <div class="catalogue-header">
        <div class="catalogue-header-title">目录</div>
        <div class="catalogue-close" @click="closeCatalogue">×</div>
      </div>
      <div class="catalogue-content" style="overflow-y: auto;">
        <div class="catalogue-list">
          <div v-for="(item, index) in catalogTab" :key="index" class="catalogue-item"
            :class="item.sectionType == 'CLAUSE' ? 'catalogue-clause' : 'catalogue-title'" :data-sort="item.sort"
            @click="jumpParagraph(item)">
            <div class="catalogue-text">{{item.sectionGraph}}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 背景遮罩 -->
    <div class="catalogue-mask" :class="{'show': showCatalogue}" @click="closeCatalogue" v-if="info.isSplitClause">
    </div>
  </div>
  <script type="text/javascript" src="https://cdn.bootcss.com/vConsole/3.3.0/vconsole.min.js"></script>
  <script type="text/javascript" src="https://isdapp.shandong.gov.cn/jmopen/jssdk/index.js"></script>
  <script defer type="text/javascript" src="../js/aes.js"></script>
  <script src="../js/SM.js" type="text/javascript" charset="utf-8"></script>
  <script src="../js/vue.min.js"></script>
  <script src="../js/vant.min.js"></script>
  <script>
    var privateKey = '00834409aff7fe64af1c1488b23c671eddbaee49d2ecc5025d6f5f2db9fe8a1da7' // 私钥
    var publicLegislativeInfoKey = '046f8ec032581100b3735a9264da46bcdf4b848af4639d262e76a8653f861588e64ed245cb197ca7387d5aa4305d5dc2d3a729b6ab6689938a37449f548fe68c1d' // 立法征询详情公钥
    var publicCommentListKey = '04fdf72f327b7ab14a4b759f49f4522c7d9e723bca81780f22117dcbd5f4ad31b9c8137e9770865bdbaf41642d844ee7d5229233ad059b428185df02eed8c63b24' // 评论列表公钥
    var publicCommentAddKey = '0415407ff89d57c67141fda6b01ce2c88be6582a12b9ed9d7e213313b7f0a8c55027fdee5ad8ecaafdae357704e1bff9068f7bb78aa233107170c13e77b24ff247'  // 评论提交公钥
    var publicAddTopicUserKey = '04e3a651aeeca72142c91953823ec51758dc16d79440ce14502145136acd9dcc63f71ddc48db1cd4a4114202e0291191a8dbe0e6615ab956d809b4548d0d07302c' // 投票提交公钥
    var publicFindSplitDraftsKey = '04a0dff3743357a932d26ad6e6c4f61b393652d5ddaceb119151727c4543f05856857b29c6e2e862b5c8f16ccebd1a89bfdf9df93e41b0f27e339062e32d1318fd' // 拆条文公钥

    var id = ''
    var app = new Vue({
      el: '#app',
      data: {
        fileImgUrl: 'https://szrd.renda.liaocheng.gov.cn:8443/lzt/image/',
        fileUrl: 'https://szrd.renda.liaocheng.gov.cn:8443/lzt/file/preview/',
        info: { "id": "929936112087076864", "title": "征集主题内容形式拆条文", "opinionType": 1, "pubOffice": "1769568285222793217", "opinionRange": "all_user", "pubTime": 1762492127165, "content": null, "startTime": 1762444800000, "endTime": 1762531200000, "opinionLayout": { "value": "1", "label": "内容形式", "name": "内容形式", "dictCode": "opinion_layout" }, "outLink": null, "attachmentIds": "db22aa61-a932-4a3e-a684-d22e8fa4e62c", "opinionContent": "<p><span style=\"font-family: 微软雅黑; font-size: 12pt;\">征集主题内容形式拆条文征集主题内容形式拆条文征集主题内容形式拆条文征集主题内容形式拆条文</span></p>", "checkedStatus": 2, "isDraft": 0, "isTop": 0, "isShare": 0, "contentFileIds": "3adbb51f-3c31-4327-95bb-26a8f4f4d4e1", "isSplitClause": 1, "isPublicVote": 0, "createDate": 1762492127162, "createBy": "1", "region": "371500", "updateDate": 1762492127162, "delFlag": 0, "businessCode": "legis_contact_point", "businessId": null, "legisOpinionType": { "value": "1", "label": "立法规划", "name": "立法规划", "dictCode": "legis_opinion_type" }, "currentMessageTemplate": null, "needTextMessageNotice": 0, "isCrossOffice": 0, "userRange": "all_user", "userIds": [], "hasUserRange": 1, "officeRange": null, "officeIds": null, "hasOfficeRange": 0, "voteTopic": null, "hasVote": null, "officeName": "", "voteTopicOptions": null, "contentFileInfos": [{ "id": "3adbb51f-3c31-4327-95bb-26a8f4f4d4e1", "originalFileName": "《湖南省先进制造业促进条例（草案）》一审稿.doc", "extName": "doc", "newFileName": "3adbb51f-3c31-4327-95bb-26a8f4f4d4e1.doc", "fileSize": 33280, "isInitFile": 0, "isKeepAlive": 0, "createBy": "1", "createByName": "Admin", "createDate": 1762492123960, "tracy": null }], "enlistStatus": "征集中", "opinionSourceType": 2, "opinionSourceCode": "legis_user", "feedback": null, "crosRegionItems": null, "attachments": [{ "id": "db22aa61-a932-4a3e-a684-d22e8fa4e62c", "originalFileName": "1点点图表代码.doc", "extName": "doc", "newFileName": "db22aa61-a932-4a3e-a684-d22e8fa4e62c.doc", "fileSize": 22016, "isInitFile": 0, "isKeepAlive": 0, "createBy": "1", "createByName": "Admin", "createDate": 1762492063308, "tracy": null }] },
        // info: {},
        commentList: [],
        // 评论相关数据
        showPopup: false,
        commentText: '',
        // 目录相关数据
        showCatalogue: false,
        catalogTab: [],
        scrollTop: 0,
        splitClauseList: [{ "id": "929936119536160768", "attachmentIds": "3adbb51f-3c31-4327-95bb-26a8f4f4d4e1", "parentId": "0", "sectionGraph": "标题", "sectionContent": "湖南省先进制造业促进条例\n", "sectionType": "TITLE", "sort": 1, "delFlag": 0, "moduleType": "legis_contact_point", "expandLinks": null, "childrens": null, "opinionNum": 0, "ownChoose": null, "enjoyCount": 0, "againstCount": 0 }, { "id": "929936119536160769", "attachmentIds": "3adbb51f-3c31-4327-95bb-26a8f4f4d4e1", "parentId": "0", "sectionGraph": "题注", "sectionContent": "（草案）\n\n", "sectionType": "CAPTION", "sort": 1, "delFlag": 0, "moduleType": "legis_contact_point", "expandLinks": null, "childrens": null, "opinionNum": 0, "ownChoose": null, "enjoyCount": 0, "againstCount": 0 }, { "id": "929936119536160770", "attachmentIds": "3adbb51f-3c31-4327-95bb-26a8f4f4d4e1", "parentId": "0", "sectionGraph": "第一条【立法目的】", "sectionContent": " 为了促进先进制造业高质量发展，打造国家重要先进制造业高地，根据相关法律法规，结合本省实际，制定本条例。 \n", "sectionType": "CLAUSE", "sort": 1, "delFlag": 0, "moduleType": "legis_contact_point", "expandLinks": null, "childrens": null, "opinionNum": 0, "ownChoose": null, "enjoyCount": 0, "againstCount": 0 }, { "id": "929936119536160771", "attachmentIds": "3adbb51f-3c31-4327-95bb-26a8f4f4d4e1", "parentId": "0", "sectionGraph": "第二条【适用范围】", "sectionContent": " 本省行政区域内促进先进制造业发展的相关活动，适用本条例。 \n本条例所称先进制造业，是指创新先进技术并综合应用于研发设计、生产制造、经营管理全过程，构建先进的产品形态、制造方式、组织架构、经营模式，促进产业整体迈向中高端并取得良好经济、社会和生态效益的制造业。既包括战略性新兴产业中的制造业，也包括利用先进适用技术、工艺、流程、材料、管理等改造提升后的传统制造业。 \n", "sectionType": "CLAUSE", "sort": 2, "delFlag": 0, "moduleType": "legis_contact_point", "expandLinks": null, "childrens": null, "opinionNum": 0, "ownChoose": null, "enjoyCount": 0, "againstCount": 0 }, { "id": "929936119536160772", "attachmentIds": "3adbb51f-3c31-4327-95bb-26a8f4f4d4e1", "parentId": "0", "sectionGraph": "第三条【基本原则】", "sectionContent": " 促进先进制造业发展，打造国家重要先进制造业高地是本省经济社会发展的重大战略，应当坚持高端化、智能化、绿色化、融合化发展方向，遵循市场主导、政府引导、创新引领、重点突破、开放带动、统筹推进的原则。\n", "sectionType": "CLAUSE", "sort": 3, "delFlag": 0, "moduleType": "legis_contact_point", "expandLinks": null, "childrens": null, "opinionNum": 0, "ownChoose": null, "enjoyCount": 0, "againstCount": 0 }, { "id": "929936119536160773", "attachmentIds": "3adbb51f-3c31-4327-95bb-26a8f4f4d4e1", "parentId": "0", "sectionGraph": "第四条【政府与部门职责】", "sectionContent": " 县级以上人民政府应当将先进 制造业发展纳入国民经济和社会发展规划，建立健全先进制造业 发展统筹推进工作机制，协调解决先进制造业发展中的重大问题。 \n县级以上人民政府工业和信息化主管部门负责先进制造业发展的规划协调、组织推进、考核评价等工作。县级以上人民政府其他有关部门按照各自职责做好促进先进制造业发展工作。 \n", "sectionType": "CLAUSE", "sort": 4, "delFlag": 0, "moduleType": "legis_contact_point", "expandLinks": null, "childrens": null, "opinionNum": 0, "ownChoose": null, "enjoyCount": 0, "againstCount": 0 }, { "id": "929936119536160774", "attachmentIds": "3adbb51f-3c31-4327-95bb-26a8f4f4d4e1", "parentId": "0", "sectionGraph": "第五条【发展规划】", "sectionContent": " 省人民政府应当组织编制先进制造业发展规划。设区的市、自治州、县（市、区）人民政府根据省先进制造业发展规划，结合实际制定实施方案。 \n省人民政府对设区的市、自治州人民政府和省级以上产业园区先进制造业发展工作进行考核，并纳入全省绩效评估体系。\n", "sectionType": "CLAUSE", "sort": 5, "delFlag": 0, "moduleType": "legis_contact_point", "expandLinks": null, "childrens": null, "opinionNum": 0, "ownChoose": null, "enjoyCount": 0, "againstCount": 0 }, { "id": "929936119536160775", "attachmentIds": "3adbb51f-3c31-4327-95bb-26a8f4f4d4e1", "parentId": "0", "sectionGraph": "第六条【产业基础】", "sectionContent": " 省、设区的市、自治州人民政府应当建立研发支持、应用鼓励、风险代偿机制，扶持基础零部件（元器件）、基础原材料、基础软件、基础工艺和基础技术领域的重点企业、重点产品、重点项目，实现产业基础再造。\n省人民政府工业和信息化主管部门应当会同有关部门建设产业技术基础公共服务平台和工业基础数据库，定期发布产业基础领域技术攻关目录。对取得重大攻关突破并实现产业化的企业或者团队按照规定给予奖励、补助。 \n", "sectionType": "CLAUSE", "sort": 6, "delFlag": 0, "moduleType": "legis_contact_point", "expandLinks": null, "childrens": null, "opinionNum": 0, "ownChoose": null, "enjoyCount": 0, "againstCount": 0 }, { "id": "929936119536160776", "attachmentIds": "3adbb51f-3c31-4327-95bb-26a8f4f4d4e1", "parentId": "0", "sectionGraph": "第七条【产业链】", "sectionContent": " 省人民政府应当推动先进制造业重点产业链发展，推进产业链重大项目建设。本省重点推动的产业链实行省、设区的市或者自治州联动的产业链链长制。 \n县级以上人民政府应当重点扶持先进制造业龙头企业，组织培育重点领域、重点环节的专精特新中小企业，在项目建设、融资担保、要素保障等方面给予支持。 \n鼓励龙头企业牵头组建产业链上下游企业共同体，协同开展技术创新和产业化协作。\n", "sectionType": "CLAUSE", "sort": 7, "delFlag": 0, "moduleType": "legis_contact_point", "expandLinks": null, "childrens": null, "opinionNum": 0, "ownChoose": null, "enjoyCount": 0, "againstCount": 0 }, { "id": "929936119536160777", "attachmentIds": "3adbb51f-3c31-4327-95bb-26a8f4f4d4e1", "parentId": "0", "sectionGraph": "第八条【产业集群】", "sectionContent": " 本省重点打造工程机械、轨道交通装备、中小航空发动机及航空航天装备等世界级产业集群和信息技术、新材料、新能源与节能等国家级产业集群，推动传统优势产业集群转型升级，培育未来及新兴支柱产业集群。 \n县级以上人民政府应当统筹产业集群区域布局，建立产业集群梯次培育体系，依托龙头企业、科研院所、行业协会、产业联盟、咨询机构等主体，建立产业集群促进组织，创新产业集群治理模式。 \n", "sectionType": "CLAUSE", "sort": 8, "delFlag": 0, "moduleType": "legis_contact_point", "expandLinks": null, "childrens": null, "opinionNum": 0, "ownChoose": null, "enjoyCount": 0, "againstCount": 0 }, { "id": "929936119536160778", "attachmentIds": "3adbb51f-3c31-4327-95bb-26a8f4f4d4e1", "parentId": "0", "sectionGraph": "第九条【产业园区】", "sectionContent": " 省、设区的市、自治州人民政府应当加强对产业园区发展先进制造业的规划引导，推动园区市场化、差异化、特色化发展，避免同质化竞争。 \n鼓励有条件的园区开展制造业高质量发展改革试验，建立园区综合评价体系，创建制造业高质量发展示范园区。 \n鼓励国家级园区与省级园区建立梯度布局、利益共享、资源互补、协同发展的共同体。 \n", "sectionType": "CLAUSE", "sort": 9, "delFlag": 0, "moduleType": "legis_contact_point", "expandLinks": null, "childrens": null, "opinionNum": 0, "ownChoose": null, "enjoyCount": 0, "againstCount": 0 }, { "id": "929936119536160779", "attachmentIds": "3adbb51f-3c31-4327-95bb-26a8f4f4d4e1", "parentId": "0", "sectionGraph": "第十条【企业培育】", "sectionContent": " 县级以上人民政府应当加强企业梯度培育，推动建设领航企业、单项冠军企业、小巨人企业等协同发展新格局。 \n鼓励国有企业引领、服务、支撑先进制造业发展。支持民营企业开展基础研究和科技创新、参与关键核心技术研发和国家重大科技项目攻关。 \n坚持竞争中性原则，保障各种所有制主体平等使用资源要素、公开公平公正参与竞争，依法保护企业产权和企业家权益。 \n", "sectionType": "CLAUSE", "sort": 10, "delFlag": 0, "moduleType": "legis_contact_point", "expandLinks": null, "childrens": null, "opinionNum": 0, "ownChoose": null, "enjoyCount": 0, "againstCount": 0 }, { "id": "929936119536160780", "attachmentIds": "3adbb51f-3c31-4327-95bb-26a8f4f4d4e1", "parentId": "0", "sectionGraph": "第十一条【智能化发展】", "sectionContent": " 县级以上人民政府应当统筹推进工业互联网等重要信息基础设施建设，支持制造业企业关键业务环节数字化、网络化、智能化改造，加快制造业生产模式和企业形态变革。 \n县级以上人民政府工业和信息化主管部门应当会同有关部门组织开展制造业数字化、网络化、智能化改造试点示范，推动建设制造业数字化服务生态。 \n鼓励大型制造业企业开展工业互联网集成应用创新，带动供应链企业数字化转型。支持中小型企业运用数字化、网络化、智能化解决方案。鼓励有条件的制造业企业建设智能工位、智能产线、智能车间、智能工厂。 \n鼓励和支持工业信息工程企业、科研机构、高等院校及其他主体提供产业数字化转型第三方服务，加强对产业数字化转型的技术支撑。 \n", "sectionType": "CLAUSE", "sort": 11, "delFlag": 0, "moduleType": "legis_contact_point", "expandLinks": null, "childrens": null, "opinionNum": 0, "ownChoose": null, "enjoyCount": 0, "againstCount": 0 }, { "id": "929936119536160781", "attachmentIds": "3adbb51f-3c31-4327-95bb-26a8f4f4d4e1", "parentId": "0", "sectionGraph": "第十二条【绿色化发展】", "sectionContent": "县级以上人民政府应当按照碳达峰、碳中和要求，支持绿色低碳技术研发推广，完善绿色低碳政策体系。健全排污权、用能权、用水权、碳排放权交易机制。 \n支持企业开展绿色制造技术改造；支持企业、园区、行业间合作，发展绿色循环经济。对符合条件的制造业企业，按照规定落实国家节能减排税收优惠政策。\n县级以上人民政府生态环境主管部门应当加强先进制造业项目选址选线、生态环保措施的指导，协调解决环境制约问题。 \n", "sectionType": "CLAUSE", "sort": 12, "delFlag": 0, "moduleType": "legis_contact_point", "expandLinks": null, "childrens": null, "opinionNum": 0, "ownChoose": null, "enjoyCount": 0, "againstCount": 0 }, { "id": "929936119536160782", "attachmentIds": "3adbb51f-3c31-4327-95bb-26a8f4f4d4e1", "parentId": "0", "sectionGraph": "第十三条【制造服务业】", "sectionContent": " 县级以上人民政府应当支持制造业企业发展信息服务、科技服务、现代物流、现代金融等生产性服务业，推动各类市场主体参与生产性服务供给，提升生产性服务业对先进制造业高质量发展的支撑作用。 \n县级以上人民政府应当支持建立服务型制造公共服务平台，鼓励企业开展工业设计服务、定制化服务、供应链管理、共享制造、检验检测认证服务、全生命周期管理、总集成总承包、节能环保服务、生产性金融服务等服务型制造。 \n", "sectionType": "CLAUSE", "sort": 13, "delFlag": 0, "moduleType": "legis_contact_point", "expandLinks": null, "childrens": null, "opinionNum": 0, "ownChoose": null, "enjoyCount": 0, "againstCount": 0 }, { "id": "929936119536160783", "attachmentIds": "3adbb51f-3c31-4327-95bb-26a8f4f4d4e1", "parentId": "0", "sectionGraph": "第十四条【创新发展】", "sectionContent": " 省人民政府及其有关部门应当建立健全产业技术创新体系和供给体系，在重点领域实施省级重大专项研发计划，布局建设重点实验室、制造业创新中心、产业创新中心、技术创新中心、企业技术中心等科技创新平台。鼓励高等院校、科研院所、高新技术企业参与国家综合性科学中心和产业创新中心建设，开展战略性新兴产业关键技术的研发与攻关活动。 \n县级以上人民政府应当落实国家支持企业创新税收优惠政策，引导企业加大研发投入。对企业提供技术转让、技术开发和与之相关的技术咨询、技术服务的，按照国家规定免征增值税。 \n县级以上人民政府应当建立激励和风险补偿机制，通过政府购买服务，推动首台（套）重大技术装备、首批次材料、首版次软件、首套件基础零部件及元器件等示范应用，推广应用新技术、 新产品、新服务。 \n支持科技服务企业开展科技成果转化咨询与服务，推进科技成果使用权、处置权和收益权改革，促进科技成果转化。鼓励军民科技协同创新，支持军地科研成果双向转化应用。 \n", "sectionType": "CLAUSE", "sort": 14, "delFlag": 0, "moduleType": "legis_contact_point", "expandLinks": null, "childrens": null, "opinionNum": 0, "ownChoose": null, "enjoyCount": 0, "againstCount": 0 }, { "id": "929936119536160784", "attachmentIds": "3adbb51f-3c31-4327-95bb-26a8f4f4d4e1", "parentId": "0", "sectionGraph": "第十五条【开放发展】", "sectionContent": " 县级以上人民政府应当支持先进制造业企业加强与“一带一路”沿线国家和地区的交流合作，开展境外投资并购、产品国际认证、申请境外专利、商标国际注册和参与国际标准制定等活动。 \n县级以上人民政府应当对接长江经济带发展、粤港澳大湾区 建设等国家战略，依托中国（湖南）自由贸易试验区建设，搭建省市县联动招商网络，吸引外资投资本地先进制造业领域。 \n鼓励跨国公司在本省设立地区总部和研发中心，建立整合贸易、物流、结算等功能的营运中心。在湘注册的外资研发机构在本省范围内开展研发活动，享受与省内研发机构同等政策。 \n", "sectionType": "CLAUSE", "sort": 15, "delFlag": 0, "moduleType": "legis_contact_point", "expandLinks": null, "childrens": null, "opinionNum": 0, "ownChoose": null, "enjoyCount": 0, "againstCount": 0 }, { "id": "929936119536160785", "attachmentIds": "3adbb51f-3c31-4327-95bb-26a8f4f4d4e1", "parentId": "0", "sectionGraph": "第十六条【质量品牌建设】", "sectionContent": " 县级以上人民政府应当推进质量品牌建设，建立工业设计中心、检验检测中心等质量基础设施，扶持质量品牌建设专业服务机构，支持企业导入先进质量管理方法，培育品牌示范企业。 \n支持先进制造业企业主导或者参与制（修）订国际标准、国家标准和行业标准。鼓励企业、社会团体研究和制定高于国家标准、行业标准的团体标准和企业标准。 \n", "sectionType": "CLAUSE", "sort": 16, "delFlag": 0, "moduleType": "legis_contact_point", "expandLinks": null, "childrens": null, "opinionNum": 0, "ownChoose": null, "enjoyCount": 0, "againstCount": 0 }, { "id": "929936119536160786", "attachmentIds": "3adbb51f-3c31-4327-95bb-26a8f4f4d4e1", "parentId": "0", "sectionGraph": "第十七条【用地保障】", "sectionContent": " 县级以上人民政府编制国土空间规划应当统筹考虑先进制造业发展需求，鼓励划设工业用地控制线，严格控制工业用地转为其他用地，在土地利用年度计划中安排一定用地指标，优先用于先进制造业项目预支使用。 \n县级以上人民政府自然资源主管部门应当对先进制造业项目用地强度、利用结构、投入产出、亩均税收等指标进行监测评价。省人民政府自然资源主管部门对节约集约用地评价排名靠前的园区给予周转用地奖励，设区的市、自治州、县（市、区）人民政府自然资源主管部门对节约集约用地评价排名靠前的企业给予容积率指标奖励。 \n", "sectionType": "CLAUSE", "sort": 17, "delFlag": 0, "moduleType": "legis_contact_point", "expandLinks": null, "childrens": null, "opinionNum": 0, "ownChoose": null, "enjoyCount": 0, "againstCount": 0 }, { "id": "929936119536160787", "attachmentIds": "3adbb51f-3c31-4327-95bb-26a8f4f4d4e1", "parentId": "0", "sectionGraph": "第十八条【财政和金融保障】", "sectionContent": " 省、设区的市、自治州人民政府以及有条件的县（市、区）人民政府应当根据实际情况安排先进制造业发展资金。省人民政府设立的相关政府产业投资基金，优先支持先进制造业发展，引导和带动社会资本支持先进制造业企业。 \n支持银行机构加强与产业投资基金、证券、保险、政府性融资担保机构等合作，鼓励金融机构加大对先进制造业的信贷支持力度，探索开展供应链金融服务，推动金融机构提高先进制造业中长期贷款和信用贷款占比。 \n", "sectionType": "CLAUSE", "sort": 18, "delFlag": 0, "moduleType": "legis_contact_point", "expandLinks": null, "childrens": null, "opinionNum": 0, "ownChoose": null, "enjoyCount": 0, "againstCount": 0 }, { "id": "929936119536160788", "attachmentIds": "3adbb51f-3c31-4327-95bb-26a8f4f4d4e1", "parentId": "0", "sectionGraph": "第十九条【人才队伍建设】", "sectionContent": " 县级以上人民政府应当加大先进制造业领域关键核心技术人才培养和引进力度，加强专业化领导干部培养和企业家队伍、技术管理团队、技能人才队伍建设。\n省人民政府人力资源社会保障主管部门应当优化先进制造业人才评价机制，赋予符合条件的大型先进制造业企业高级职称评审权。具体办法由省人民政府人力资源社会保障主管部门会同工业和信息化主管部门制定。 \n省人民政府人力资源社会保障主管部门、教育主管部门、工业和信息化主管部门应当加强先进制造业技能培训和职业教育，支持职业院校开展补贴性培训，完善产教融合的人才培养模式，通过政府购买服务、校企合作等方式支持、引导社会力量参与紧缺人才培养。 \n", "sectionType": "CLAUSE", "sort": 19, "delFlag": 0, "moduleType": "legis_contact_point", "expandLinks": null, "childrens": null, "opinionNum": 0, "ownChoose": null, "enjoyCount": 0, "againstCount": 0 }, { "id": "929936119536160789", "attachmentIds": "3adbb51f-3c31-4327-95bb-26a8f4f4d4e1", "parentId": "0", "sectionGraph": "第二十条【监督责任】", "sectionContent": " 省人民政府应当定期向省人民代表大会常务委员会报告先进制造业促进工作情况。 \n县级以上人民代表大会常务委员会应当加强对本行政\n区域内先进制造业促进工作的监督。 \n设区的市、自治州人民政府、省级以上产业园区主要领导干部离任时，应当将其任职期间先进制造业促进工作纳入离任审计范围，审计结果作为干部评价使用的参考依据。 \n", "sectionType": "CLAUSE", "sort": 20, "delFlag": 0, "moduleType": "legis_contact_point", "expandLinks": null, "childrens": null, "opinionNum": 0, "ownChoose": null, "enjoyCount": 0, "againstCount": 0 }, { "id": "929936119536160790", "attachmentIds": "3adbb51f-3c31-4327-95bb-26a8f4f4d4e1", "parentId": "0", "sectionGraph": "第二十一条【实施时间】", "sectionContent": " 本条例自 年 月 日起施行。", "sectionType": "CLAUSE", "sort": 21, "delFlag": 0, "moduleType": "legis_contact_point", "expandLinks": null, "childrens": null, "opinionNum": 0, "ownChoose": null, "enjoyCount": 0, "againstCount": 0 }]
        // splitClauseList: []
      },
      mounted () {
        var vConsole = new VConsole() // 初始化
        const urlParams = new URLSearchParams(window.location.search)
        id = urlParams.get('id')
        if (id) {
          // this.getInfo()
          // this.getCommentList()
        }
      },
      methods: {
        // 获取立法征询详情
        getInfo () {
          var that = this
          var appid = 'lcsszrduwgvg'
          var interfaceid = 'legislationOpinionInfo'
          var interfacecontent = { detailId: id, isPublic: true, userId: JSON.parse(localStorage.getItem('gphone')) }
          let extraData = {
            header: { 'u-login-areaId': '371500', 'u-terminal': 'PUBLIC' }
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicLegislativeInfoKey)
          vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
            try {
              var ret = JSON.parse(SM.decrypt(res, privateKey))
              ret.data.isSplitClause ? this.getSplitClauseData(ret.data.checkedStatus, ret.data.contentFileIds) : ''
              that.info = ret.data
            } catch (error) {
              // 在这里处理异常情况
              console.log('解析JSON时发生错误：', error)
              that.getInfo()
            }
          })
        },

        // 获取评论列表
        getCommentList () {
          var that = this
          var appid = 'lcsszrduwgvg'
          var interfaceid = 'findOpionComment'
          var interfacecontent = {
            checkedStatus: '',
            pageNo: 1,
            pageSize: 99,
            query: { businessCode: 'legis_contact_point', businessId: id, extRed: that.info.opinionSourceCode || '', extYellow: '' }
          }
          let extraData = {
            header: { 'u-login-areaId': '371500', 'u-terminal': 'PUBLIC' }
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicCommentListKey)
          vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
            try {
              var ret = JSON.parse(SM.decrypt(res, privateKey))
              that.commentList = ret.data
            } catch (error) {
              // 在这里处理异常情况
              console.log('解析JSON时发生错误：', error)
              that.getCommentList()
            }
          })
        },

        // 附件预览
        clickfile (attachment) {
          console.log('文件信息:', this.fileUrl + attachment.id);
          window.location.href = 'https://cszysoft.com/appShare/previews/index.html?url=' + this.fileUrl + attachment.id
        },

        // 打开外部链接
        openOutLink () {
          if (!this.info.outLink) {
            return
          }
          window.location.href = this.info.outLink
        },

        // 显示评论弹窗
        showCommentPopup () {
          this.showPopup = true
          this.commentText = ''
          // 延迟聚焦到文本框
          this.$nextTick(() => {
            const textarea = document.querySelector('.comment_textarea')
            if (textarea) {
              textarea.focus()
            }
          })
        },

        // 隐藏评论弹窗
        hideCommentPopup () {
          this.showPopup = false
          this.commentText = ''
        },

        // 提交评论
        submitComment () {
          if (!this.commentText.trim()) {
            vant.Toast('请输入评论内容')
            return
          }
          // 显示加载中提示
          vant.Toast.loading({
            mask: true,
            message: '提交中...'
          })
          var that = this
          var appid = 'lcsszrduwgvg'
          var interfaceid = 'commentAdds'
          var interfacecontent = {
            form: {
              businessCode: 'legis_contact_point',
              businessId: id,
              commentContent: this.commentText,
              extBlue: 2,
              extGreen: id,
              extRed: this.info.opinionSourceCode || '',
              extYellow: id,
              jordan: 'contact_point',
              terminalName: 'PUBLIC',
              commentUserName: JSON.parse(localStorage.getItem('gname'))
            }
          }
          let extraData = {
            header: {
              'u-login-areaId': '371500',
              'u-terminal': 'PUBLIC'
            }
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicCommentAddKey)
          vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
            try {
              var ret = JSON.parse(SM.decrypt(res, privateKey))
              if (ret.code == 200) {
                vant.Toast.success('评论发表成功！');
                setTimeout(() => {
                  this.hideCommentPopup()
                }, 1000)
              }
            } catch (error) {
              // 在这里处理异常情况
              console.log('提交留言结果发生错误：', error)
            }
          })
        },

        // 选择投票项
        selectOption (index) {
          // 如果已经投过票，则不允许再次选择
          if (this.info.hasVote) {
            vant.Toast('您已经投过票了！')
            return
          }
          if (this.info.enlistStatus == "已结束" || this.info.enlistStatus == "未开始") {
            vant.Toast('征集' + this.info.enlistStatus)
            return false
          }
          const maxVote = this.info.voteTopic ? this.info.voteTopic.maxVote : 1
          // 获取当前选中的数量
          const currentSelectedCount = this.info.voteTopicOptions.filter(item => item.choose).length
          // 如果是单选，取消其他选项的选中状态
          if (maxVote === 1) {
            this.info.voteTopicOptions.forEach((item, idx) => {
              item.choose = idx === index
            })
          } else {
            // 多选情况下，如果点击已选中的选项，则取消选中
            if (this.info.voteTopicOptions[index].choose) {
              this.info.voteTopicOptions[index].choose = false
            } else {
              // 如果未达到最大选择数量，则选中
              if (currentSelectedCount < maxVote) {
                this.info.voteTopicOptions[index].choose = true
              } else {
                vant.Toast('最多只能选择' + maxVote + '项')
                return
              }
            }
          }
          this.info = { ...this.info }
        },

        // 投票
        submitVote () {
          // 如果已经投过票，则不允许再次选择
          if (this.info.hasVote) {
            vant.Toast('您已经投过票了！')
            return
          }
          if (this.info.enlistStatus == "已结束" || this.info.enlistStatus == "未开始") {
            vant.Toast('征集' + this.info.enlistStatus)
            return false
          }
          // 获取选中的选项
          const selectedOptions = this.info.voteTopicOptions.filter(item => item.choose)
          if (selectedOptions.length === 0) {
            vant.Toast('请至少选择一项征集')
            return
          }
          // 显示加载中提示
          vant.Toast.loading({
            mask: true,
            message: '提交中...'
          })
          var appid = 'lcsszrduwgvg'
          var interfaceid = 'addTopicUser'
          var interfacecontent = {
            form: {
              businessCode: "legislationOpinion",
              extRed: "legis_office",
              jordan: "contact_point",
              userId: JSON.parse(localStorage.getItem('gphone')),
              userName: JSON.parse(localStorage.getItem('gname'))
            },
            optionIds: selectedOptions.map(item => item.id),
            topicId: this.info.voteTopic.id
          }
          let extraData = {
            header: {
              'u-login-areaId': '371500',
              'u-terminal': 'PUBLIC'
            }
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicAddTopicUserKey)
          vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
            try {
              var ret = JSON.parse(SM.decrypt(res, privateKey))
              console.log('投票结果ret===>', ret)
              if (ret.code == 200) {
                vant.Toast.success('投票成功！');
                setTimeout(() => {
                  this.getInfo()
                }, 1000)
              }
            } catch (error) {
              // 在这里处理异常情况
              console.log('投票结果发生错误：', error)
            }
          })
        },

        // 获取拆条目
        getSplitClauseData (checkedStatus, contentFileIds) {
          var that = this
          var appid = 'lcsszrduwgvg'
          var interfaceid = 'findSplitDrafts'
          var interfacecontent = {
            businessCode: 'legis_contact_point',
            checkedStatus: checkedStatus,
            fileId: contentFileIds,
            manage: false,
          }
          let extraData = {
            header: { 'u-login-areaId': '371500', 'u-terminal': 'PUBLIC' }
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicFindSplitDraftsKey)
          vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
            try {
              var ret = JSON.parse(SM.decrypt(res, privateKey))
              that.splitClauseList = ret.data
            } catch (error) {
              // 在这里处理异常情况
              console.log('解析JSON时发生错误：', error)
              that.getSplitClauseData()
            }
          })
        },

        // 点击打开条文
        openDetailsWin (item) {
          window.location.href = './provisionDetails.html?id=' + item.id + '&attachmentIds=' + item.attachmentIds + '&checkedStatus=' + this.info.checkedStatus + '&extGreen=' + id
        },

        // 打开目录
        openCatalogue () {
          this.showCatalogue = true
          // 生成目录数据
          this.catalogTab = this.splitClauseList.filter(item =>
            item.sectionType === 'CLAUSE'
          )
          // 阻止背景页面滚动
          document.body.classList.add('catalogue-open')
          // 记录当前滚动位置
          this.scrollTop = document.documentElement.scrollTop || document.body.scrollTop
          document.body.style.top = -this.scrollTop + 'px'
        },

        // 关闭目录
        closeCatalogue () {
          this.showCatalogue = false
          // 恢复背景页面滚动
          document.body.classList.remove('catalogue-open')
          document.body.style.top = ''
          // 恢复滚动位置
          if (this.scrollTop !== undefined) {
            document.documentElement.scrollTop = this.scrollTop
            document.body.scrollTop = this.scrollTop
          }
        },

        // 跳转到指定段落
        jumpParagraph (item) {
          const targetElement = document.getElementById('noti' + item.sort)
          if (targetElement) {
            targetElement.scrollIntoView({ behavior: 'smooth', block: 'start' })
            this.closeCatalogue()
          }
        },
      }
    })
    function formatTime (date) {
      var date = new Date(date)
      var year = date.getFullYear()
      var month = date.getMonth() + 1
      var day = date.getDate()
      var hour = date.getHours()
      var minute = date.getMinutes()
      var second = date.getSeconds()
      // 返回年月日时分秒
      return [year, month, day].map(formatNumber).join('-') + ' ' + [hour, minute, second].map(formatNumber).join(':')
    }

    function formatNumber (n) {
      n = n.toString()
      return n[1] ? n : '0' + n
    }
  </script>
</body>

</html>