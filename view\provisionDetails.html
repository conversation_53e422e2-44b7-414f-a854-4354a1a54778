<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <title>详情</title>
  <link rel="stylesheet" href="../css/vant.css" />
  <link rel="stylesheet" href="../css/common.css" />
  <style type="text/css">
    [v-cloak] {
      display: none;
    }

    .body_box {
      background-color: #FFFFFF;
      padding-bottom: 60px;
    }

    /* 全部评论 */
    .comment_section {
      padding: 15px 20px;
      border-top: 10px solid #f8f8f8;
    }

    .comment_list {
      padding: 8px 0;
    }

    .comment_item {
      margin-bottom: 10px;
      padding-bottom: 10px;
      border-bottom: 1px solid #f5f5f5;
    }

    .comment_item:last-child {
      border-bottom: none;
      margin-bottom: 0;
      padding-bottom: 0;
    }

    .comment_user {
      margin-bottom: 10px;
    }

    .comment_avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      margin-right: 10px;
    }

    .comment_nickname {
      font-size: 16px;
      color: #333;
      font-weight: 600;
    }

    .comment_content {
      font-weight: 400;
      font-size: 15px;
      color: #333;
      line-height: 20px;
      margin-bottom: 10px;
    }

    .comment_time {
      font-size: 14px;
      color: #999;
    }

    .nodata {
      margin: 30px 0;
      text-align: center;
      color: #ccc;
      width: 100%;
      font-size: 14px;
    }

    /* 评论输入框样式 */
    .comment_input_section {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      background: #fff;
      padding: 10px 15px;
      border-top: 1px solid #e5e5e5;
      z-index: 100;
    }

    .comment_input_box {
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .comment_input {
      flex: 1;
      height: 36px;
      border: 1px solid #e5e5e5;
      border-radius: 18px;
      padding: 0 15px;
      font-size: 14px;
      color: #333;
      background: #f8f8f8;
    }

    .comment_input::placeholder {
      color: #999;
    }

    .comment_num {
      font-size: 14px;
      color: #999;
    }

    .comment_count {
      font-size: 12px;
      color: #999;
      display: flex;
      align-items: center;
      gap: 5px;
    }

    .comment_icon {
      width: 23px;
      height: 23px;
      margin-left: 5px;
    }

    /* 评论弹窗样式 */
    .comment_popup {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      z-index: 1000;
      display: flex;
      align-items: flex-end;
    }

    .comment_popup_content {
      width: 100%;
      background: #fff;
      border-radius: 12px 12px 0 0;
      padding: 20px;
      animation: slideUp 0.3s ease-out;
    }

    @keyframes slideUp {
      from {
        transform: translateY(100%);
      }

      to {
        transform: translateY(0);
      }
    }

    .comment_popup_header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }

    .comment_popup_title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }

    .comment_popup_close {
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #999;
      font-size: 18px;
      cursor: pointer;
    }

    .comment_textarea {
      width: 100%;
      min-height: 120px;
      border: 1px solid #e5e5e5;
      border-radius: 8px;
      padding: 12px;
      font-size: 14px;
      color: #333;
      resize: none;
      outline: none;
      box-sizing: border-box;
    }

    .comment_textarea::placeholder {
      color: #999;
    }

    .comment_popup_footer {
      display: flex;
      gap: 15px;
      margin-top: 20px;
    }

    .comment_btn {
      flex: 1;
      height: 44px;
      border: none;
      border-radius: 22px;
      font-size: 16px;
      cursor: pointer;
      transition: all 0.3s;
    }

    .comment_btn_cancel {
      background: #f5f5f5;
      color: #666;
    }

    .comment_btn_submit {
      background: #1890ff;
      color: #fff;
    }

    /* 拆条文 */
    .provision {
      padding: 16px;
      border-top: 10px solid #f8f8f8;
    }

    .m20 {
      margin-bottom: 10px;
    }

    .m24 {
      margin-bottom: 12px;
    }

    .m40 {
      margin-bottom: 20px;
    }
  </style>
</head>

<body>
  <div class="body_box" id="app">
    <!-- 拆条文 -->
    <div class="provision">
      <div v-for="(item,index) in splitClauseList" :key="index">
        <template v-if="item.sectionType == 'SECTION'">
          <div class="m20">
            <div style="color:#333;width:100%;" v-html="item.sectionGraph+item.sectionContent"></div>
          </div>
        </template>
        <template v-else-if="item.sectionType == 'CLAUSE'">
          <div :id="'noti'+item.sort" data-id="{{item.id}}" data-attachmentIds="{{item.attachmentIds}}"
            @click="openDetailsWin(item)">
            <div class="m20">
              <div style="color:#333;width:100%;line-height: 30px;font-size: 16px;"
                v-html="item.sectionGraph + item.sectionContent"></div>
            </div>
          </div>
        </template>
        <template v-else-if="item.sectionType == 'TITLE'">
          <div class="m40">
            <div style="color:#333;text-align: center;width:100%;" v-html="item.sectionContent"></div>
          </div>
        </template>
        <template v-else>
          <div class="m24" v-html="item.sectionContent">
            <div style="color:#333;width:100%;" v-html="item.sectionContent"></div>
          </div>
        </template>
      </div>
    </div>

    <!-- 全部评论 -->
    <div class="comment_section">
      <div class="section-header flex_box flex_align_center" style="margin-bottom: 10px;">
        <div class="section-line"></div>
        <span class="section-title">全部评论</span>
      </div>
      <div class="comment_list">
        <template v-if="commentList&&commentList.length>0">
          <div class="comment_item" v-for="(item,index) in commentList" :key="index">
            <div class="comment_user flex_box flex_align_center">
              <image class="comment_avatar" :src="item.headImg?fileImgUrl+item.headImg:'../img/def_head_img.jpg'"
                mode="aspectFill" />
              <div class="comment_nickname">{{item.createName}}</div>
            </div>
            <div class="comment_content">{{item.commentContent}}</div>
            <div class="comment_time">{{formatTime(item.createDate)}}</div>
          </div>
        </template>
        <template v-else>
          <div class="nodata">暂无数据</div>
        </template>
      </div>
    </div>

    <!-- 评论输入框 -->
    <div class="comment_input_section">
      <div class="comment_input_box">
        <input type="text" class="comment_input" placeholder="发表评论" readonly @click="showCommentPopup" />
        <div class="comment_count">
          <span class="comment_num">{{commentList.length}}</span>
          <image class="comment_icon" src="../img/icon_common.png" mode="aspectFill" />
        </div>
      </div>
    </div>

    <!-- 评论弹窗 -->
    <div class="comment_popup" v-if="showPopup" @click="hideCommentPopup">
      <div class="comment_popup_content" @click.stop v-cloak>
        <div class="comment_popup_header">
          <div class="comment_popup_title">发表评论</div>
          <div class="comment_popup_close" @click="hideCommentPopup">×</div>
        </div>
        <textarea class="comment_textarea" v-model="commentText" placeholder="请输入您的评论" maxlength="500"></textarea>
        <div class="comment_popup_footer">
          <button class="comment_btn comment_btn_cancel" @click="hideCommentPopup">取消</button>
          <button class="comment_btn comment_btn_submit" @click="submitComment">发送</button>
        </div>
      </div>
    </div>
  </div>
  <script type="text/javascript" src="https://cdn.bootcss.com/vConsole/3.3.0/vconsole.min.js"></script>
  <script type="text/javascript" src="https://isdapp.shandong.gov.cn/jmopen/jssdk/index.js"></script>
  <script defer type="text/javascript" src="../js/aes.js"></script>
  <script src="../js/SM.js" type="text/javascript" charset="utf-8"></script>
  <script src="../js/vue.min.js"></script>
  <script src="../js/vant.min.js"></script>
  <script>
    var privateKey = '00834409aff7fe64af1c1488b23c671eddbaee49d2ecc5025d6f5f2db9fe8a1da7' // 私钥
    var publicCommentListKey = '04fdf72f327b7ab14a4b759f49f4522c7d9e723bca81780f22117dcbd5f4ad31b9c8137e9770865bdbaf41642d844ee7d5229233ad059b428185df02eed8c63b24' // 评论列表公钥
    var publicCommentAddKey = '0415407ff89d57c67141fda6b01ce2c88be6582a12b9ed9d7e213313b7f0a8c55027fdee5ad8ecaafdae357704e1bff9068f7bb78aa233107170c13e77b24ff247'  // 评论提交公钥
    var publicFindSplitDraftsKey = '04a0dff3743357a932d26ad6e6c4f61b393652d5ddaceb119151727c4543f05856857b29c6e2e862b5c8f16ccebd1a89bfdf9df93e41b0f27e339062e32d1318fd' // 拆条文公钥

    var id = '', attachmentIds = '', checkedStatus = '', extGreen = ''
    var app = new Vue({
      el: '#app',
      data: {
        fileImgUrl: 'https://szrd.renda.liaocheng.gov.cn:8443/lzt/image/',
        fileUrl: 'https://szrd.renda.liaocheng.gov.cn:8443/lzt/file/preview/',
        commentList: [],
        // 评论相关数据
        showPopup: false,
        commentText: '',
        splitClauseList: []
      },
      mounted () {
        var vConsole = new VConsole() // 初始化
        const urlParams = new URLSearchParams(window.location.search)
        id = urlParams.get('id')
        attachmentIds = urlParams.get('attachmentIds')
        checkedStatus = urlParams.get('checkedStatus')
        extGreen = urlParams.get('extGreen')
        console.log('id===>', id)
        console.log('attachmentIds===>', attachmentIds)
        console.log('checkedStatus===>', checkedStatus)
        console.log('extGreen===>', extGreen)
        if (id) {
          this.getSplitClauseData()
          this.getCommentList()
        }
      },
      methods: {
        // 获取拆条目
        getSplitClauseData () {
          var that = this
          var appid = 'lcsszrduwgvg'
          var interfaceid = 'findSplitDrafts'
          var interfacecontent = {
            businessCode: 'legis_contact_point',
            checkedStatus: checkedStatus,
            draftId: id,
            fileId: attachmentIds,
            manage: false,
          }
          let extraData = {
            header: { 'u-login-areaId': '371500', 'u-terminal': 'PUBLIC' }
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicFindSplitDraftsKey)
          vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
            try {
              var ret = JSON.parse(SM.decrypt(res, privateKey))
              that.splitClauseList = ret.data
            } catch (error) {
              // 在这里处理异常情况
              console.log('解析JSON时发生错误：', error)
              that.getSplitClauseData()
            }
          })
        },

        // 获取评论列表
        getCommentList () {
          var that = this
          var appid = 'lcsszrduwgvg'
          var interfaceid = 'findOpionComment'
          var interfacecontent = {
            checkedStatus: '',
            pageNo: 1,
            pageSize: 99,
            query: { businessCode: 'legis_contact_point', businessId: id, extRed: 'legis_user', extYellow: '' }
          }
          let extraData = {
            header: { 'u-login-areaId': '371500', 'u-terminal': 'PUBLIC' }
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicCommentListKey)
          vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
            try {
              var ret = JSON.parse(SM.decrypt(res, privateKey))
              that.commentList = ret.data
            } catch (error) {
              // 在这里处理异常情况
              console.log('解析JSON时发生错误：', error)
              that.getCommentList()
            }
          })
        },

        // 显示评论弹窗
        showCommentPopup () {
          this.showPopup = true
          this.commentText = ''
          // 延迟聚焦到文本框
          this.$nextTick(() => {
            const textarea = document.querySelector('.comment_textarea')
            if (textarea) {
              textarea.focus()
            }
          })
        },

        // 隐藏评论弹窗
        hideCommentPopup () {
          this.showPopup = false
          this.commentText = ''
        },

        // 提交评论
        submitComment () {
          if (!this.commentText.trim()) {
            vant.Toast('请输入评论内容')
            return
          }
          // 显示加载中提示
          vant.Toast.loading({
            mask: true,
            message: '提交中...'
          })
          var that = this
          var appid = 'lcsszrduwgvg'
          var interfaceid = 'commentAdds'
          var interfacecontent = {
            form: {
              businessCode: 'legis_contact_point',
              businessId: id,
              commentContent: this.commentText,
              extBlue: 2,
              extGreen: extGreen,
              extRed: 'legis_user',
              extYellow: '',
              jordan: 'contact_point',
              terminalName: 'PUBLIC',
              commentUserName: JSON.parse(localStorage.getItem('gname'))
            }
          }
          let extraData = {
            header: {
              'u-login-areaId': '371500',
              'u-terminal': 'PUBLIC'
            }
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicCommentAddKey)
          vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
            try {
              var ret = JSON.parse(SM.decrypt(res, privateKey))
              if (ret.code == 200) {
                vant.Toast.success('评论发表成功！');
                setTimeout(() => {
                  this.hideCommentPopup()
                }, 1000)
              }
            } catch (error) {
              // 在这里处理异常情况
              console.log('提交留言结果发生错误：', error)
            }
          })
        },

      }
    })
    function formatTime (date) {
      var date = new Date(date)
      var year = date.getFullYear()
      var month = date.getMonth() + 1
      var day = date.getDate()
      var hour = date.getHours()
      var minute = date.getMinutes()
      var second = date.getSeconds()
      // 返回年月日时分秒
      return [year, month, day].map(formatNumber).join('-') + ' ' + [hour, minute, second].map(formatNumber).join(':')
    }

    function formatNumber (n) {
      n = n.toString()
      return n[1] ? n : '0' + n
    }
  </script>
</body>

</html>